'use client';

import React, { useEffect, useRef } from 'react';
import { Bo<PERSON>, User, Loader2 } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import { ChatbotMessagesProps } from './types/chatbot-types';

export const ChatbotMessages: React.FC<ChatbotMessagesProps> = ({
  messages,
  isLoading,
}) => {
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isLoading]);

  const formatTime = (date?: Date) => {
    if (!date) return '';
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  return (
    <ScrollArea
      className="flex-1 px-4 py-4 overflow-hidden"
      ref={scrollAreaRef}
    >
      <div className="space-y-6 overflow-hidden">
        {messages.length === 0 && !isLoading && (
          <div className="text-center py-12 px-4">
            <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-primary/10 mx-auto mb-6">
              <Bot className="h-8 w-8 text-primary" />
            </div>
            <h4 className="font-semibold text-base mb-3 break-words">
              Welcome to AI Assistant
            </h4>
            <p className="text-sm text-muted-foreground max-w-[300px] mx-auto break-words leading-relaxed">
              Ask me questions about your blueprint files or the drawings you've
              created. I can help analyze architectural plans and your custom
              annotations.
            </p>
          </div>
        )}

        {messages.map((message) => (
          <div
            key={message.id}
            className={cn(
              'flex gap-3',
              message.role === 'user' ? 'justify-end' : 'justify-start',
            )}
          >
            {message.role === 'assistant' && (
              <div className="flex items-center justify-center w-9 h-9 rounded-xl bg-primary/10 flex-shrink-0">
                <Bot className="h-5 w-5 text-primary" />
              </div>
            )}

            <div
              className={cn(
                'max-w-[300px] rounded-2xl px-4 py-3 text-sm overflow-hidden',
                message.role === 'user'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-muted/60 text-foreground',
              )}
            >
              <p className="whitespace-pre-wrap break-words break-all overflow-wrap-anywhere leading-relaxed">
                {message.content}
              </p>
              <div className="flex items-center justify-between mt-2">
                <span className="text-xs opacity-60">
                  {formatTime(message.createdAt)}
                </span>
                {/* Note: UIMessage doesn't have contextType, could be added via annotations if needed */}
              </div>
            </div>

            {message.role === 'user' && (
              <div className="flex items-center justify-center w-9 h-9 rounded-xl bg-primary/10 flex-shrink-0">
                <User className="h-5 w-5 text-primary" />
              </div>
            )}
          </div>
        ))}

        {isLoading && (
          <div className="flex gap-3 justify-start">
            <div className="flex items-center justify-center w-9 h-9 rounded-xl bg-primary/10 flex-shrink-0">
              <Bot className="h-5 w-5 text-primary" />
            </div>
            <div className="bg-muted/60 rounded-2xl px-4 py-3 text-sm max-w-[300px] overflow-hidden">
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin flex-shrink-0" />
                <span className="text-foreground">Thinking...</span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>
    </ScrollArea>
  );
};
