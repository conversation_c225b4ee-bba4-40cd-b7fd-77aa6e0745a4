'use client';

import React, { useRef } from 'react';
import { ArrowUp, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { AIModel, AI_MODEL_OPTIONS } from './types/chatbot-types';

interface ChatbotInputProps {
  input: string;
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleSubmit: (e: React.FormEvent) => void;
  isLoading: boolean;
  disabled?: boolean;
  selectedModel: AIModel;
  onModelChange: (model: AIModel) => void;
}

export const ChatbotInput: React.FC<ChatbotInputProps> = ({
  input,
  handleInputChange,
  handleSubmit,
  isLoading,
  disabled = false,
  selectedModel,
  onModelChange,
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (input.trim() && !isLoading && !disabled) {
        handleSubmit(e);
      }
    }
  };

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    handleInputChange(e);

    // Auto-resize textarea
    const textarea = e.target;
    textarea.style.height = 'auto';
    textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
  };

  const isSubmitDisabled = !input.trim() || isLoading || disabled;

  return (
    <div className="p-4 border-t bg-background">
      <form onSubmit={handleSubmit}>
        {/* Big container matching the UI */}
        <div className="bg-white border border-gray-200 rounded-3xl p-4 shadow-sm hover:shadow-md transition-all duration-200">
          {/* Input area - spacious for easy typing */}
          <div className="mb-3">
            <Textarea
              ref={textareaRef}
              value={input}
              onChange={handleTextareaChange}
              onKeyDown={handleKeyDown}
              placeholder="Ask ai anything about the drawing"
              disabled={isLoading || disabled}
              className={cn(
                'min-h-[60px] max-h-[120px] resize-none text-base border-0 p-0',
                'focus-visible:ring-0 focus-visible:ring-offset-0 shadow-none',
                'placeholder:text-gray-400 bg-transparent w-full',
                'scrollbar-thin scrollbar-thumb-gray-200 scrollbar-track-transparent',
              )}
              rows={2}
            />
          </div>

          {/* Bottom row: Model selector left, Submit button right */}
          <div className="flex items-center justify-between">
            {/* Model selector on the left */}
            <div className="flex-shrink-0">
              <Select value={selectedModel} onValueChange={onModelChange}>
                <SelectTrigger className="w-[100px] h-8 text-xs border-0 bg-gray-50 hover:bg-gray-100 rounded-lg shadow-none focus:ring-0 focus:ring-offset-0">
                  <SelectValue />
                  <ChevronDown className="h-3 w-3 opacity-50" />
                </SelectTrigger>
                <SelectContent className="rounded-lg border shadow-lg min-w-[140px]">
                  {AI_MODEL_OPTIONS.map((option) => (
                    <SelectItem
                      key={option.value}
                      value={option.value}
                      className="text-xs rounded focus:bg-primary/10"
                    >
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Submit button on the right - fully round and small */}
            <div className="flex-shrink-0">
              <Button
                type="submit"
                size="icon"
                disabled={isSubmitDisabled}
                className={cn(
                  'h-10 w-10 rounded-full transition-all duration-200',
                  isSubmitDisabled
                    ? 'bg-gray-200 text-gray-400 cursor-not-allowed hover:bg-gray-200'
                    : 'bg-primary hover:bg-primary/90 text-primary-foreground shadow-sm hover:shadow-md',
                )}
              >
                <ArrowUp className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};
