'use client';

import React, { useRef } from 'react';
import { Send, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { AIModel, AI_MODEL_OPTIONS } from './types/chatbot-types';

interface ChatbotInputProps {
  input: string;
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleSubmit: (e: React.FormEvent) => void;
  isLoading: boolean;
  disabled?: boolean;
  selectedModel: AIModel;
  onModelChange: (model: AIModel) => void;
}

export const ChatbotInput: React.FC<ChatbotInputProps> = ({
  input,
  handleInputChange,
  handleSubmit,
  isLoading,
  disabled = false,
  selectedModel,
  onModelChange,
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (input.trim() && !isLoading && !disabled) {
        handleSubmit(e);
      }
    }
  };

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    handleInputChange(e);

    // Auto-resize textarea
    const textarea = e.target;
    textarea.style.height = 'auto';
    textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
  };

  const isSubmitDisabled = !input.trim() || isLoading || disabled;

  return (
    <div className="p-4 border-t bg-background">
      <form onSubmit={handleSubmit} className="relative">
        {/* Single integrated input container */}
        <div className="relative flex items-end gap-2 p-3 bg-white border border-gray-200 rounded-2xl shadow-sm hover:shadow-md transition-all duration-200">
          {/* Model selector - positioned inside left */}
          <div className="flex-shrink-0">
            <Select value={selectedModel} onValueChange={onModelChange}>
              <SelectTrigger className="w-[90px] h-7 text-xs border-0 bg-gray-50 hover:bg-gray-100 rounded-lg shadow-none focus:ring-0 focus:ring-offset-0">
                <SelectValue />
                <ChevronDown className="h-3 w-3 opacity-50" />
              </SelectTrigger>
              <SelectContent className="rounded-lg border shadow-lg min-w-[140px]">
                {AI_MODEL_OPTIONS.map((option) => (
                  <SelectItem
                    key={option.value}
                    value={option.value}
                    className="text-xs rounded focus:bg-primary/10"
                  >
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Textarea input */}
          <div className="flex-1 min-w-0">
            <Textarea
              ref={textareaRef}
              value={input}
              onChange={handleTextareaChange}
              onKeyDown={handleKeyDown}
              placeholder="Ask ai anything about the drawing"
              disabled={isLoading || disabled}
              className={cn(
                'min-h-[40px] max-h-[120px] resize-none text-sm border-0 p-0',
                'focus-visible:ring-0 focus-visible:ring-offset-0 shadow-none',
                'placeholder:text-gray-400 bg-transparent',
                'scrollbar-thin scrollbar-thumb-gray-200 scrollbar-track-transparent',
              )}
              rows={1}
            />
          </div>

          {/* Submit button - positioned inside right */}
          <div className="flex-shrink-0">
            <Button
              type="submit"
              size="icon"
              disabled={isSubmitDisabled}
              className={cn(
                'h-8 w-8 rounded-lg transition-all duration-200',
                isSubmitDisabled
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed hover:bg-gray-100'
                  : 'bg-blue-500 hover:bg-blue-600 text-white shadow-sm hover:shadow-md',
              )}
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
};
