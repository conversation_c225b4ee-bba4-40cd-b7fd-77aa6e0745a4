'use client';

import React, { useRef } from 'react';
import { Send } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import { ModelSelector } from './ModelSelector';
import { AIModel } from './types/chatbot-types';

interface ChatbotInputProps {
  input: string;
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleSubmit: (e: React.FormEvent) => void;
  isLoading: boolean;
  disabled?: boolean;
  selectedModel: AIModel;
  onModelChange: (model: AIModel) => void;
}

export const ChatbotInput: React.FC<ChatbotInputProps> = ({
  input,
  handleInputChange,
  handleSubmit,
  isLoading,
  disabled = false,
  selectedModel,
  onModelChange,
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    handleInputChange(e);

    // Auto-resize textarea
    const textarea = e.target;
    textarea.style.height = 'auto';
    textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
  };

  return (
    <div className="p-4">
      {/* Expanded input form with integrated model selector */}
      <form onSubmit={handleSubmit} className="space-y-3">
        {/* Model selector integrated at top */}
        <div className="flex justify-end">
          <ModelSelector
            selectedModel={selectedModel}
            onModelChange={onModelChange}
          />
        </div>

        {/* Large input area with send button */}
        <div className="flex gap-3 items-end">
          <div className="flex-1">
            <Textarea
              ref={textareaRef}
              value={input}
              onChange={handleTextareaChange}
              onKeyDown={handleKeyDown}
              placeholder="Ask ai anything about the drawing"
              disabled={isLoading || disabled}
              className={cn(
                'min-h-[60px] max-h-[120px] resize-none text-base',
                'border-2 rounded-2xl px-6 py-4',
                'focus-visible:ring-0 focus-visible:border-primary',
                'placeholder:text-muted-foreground/60',
                'bg-muted/20 hover:bg-muted/30 transition-colors',
              )}
              rows={2}
            />
          </div>
          <Button
            type="submit"
            size="icon"
            disabled={!input.trim() || isLoading || disabled}
            className="h-[60px] w-[60px] flex-shrink-0 rounded-2xl bg-primary hover:bg-primary/90 transition-all"
          >
            <Send className="h-6 w-6" />
          </Button>
        </div>
      </form>
    </div>
  );
};
