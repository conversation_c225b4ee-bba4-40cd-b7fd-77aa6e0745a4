'use client';

import React from 'react';
import { <PERSON>, <PERSON>us, Bo<PERSON> } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ChatbotHeaderProps } from './types/chatbot-types';

export const ChatbotHeader: React.FC<ChatbotHeaderProps> = ({
  projectName,
  onClose,
  onMinimize,
}) => {
  return (
    <div className="flex items-center justify-between p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex items-center gap-3">
        <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-primary/10">
          <Bot className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h3 className="font-semibold text-base">Chat with AI</h3>
          {projectName && projectName !== 'hehe' && (
            <p className="text-sm text-muted-foreground truncate max-w-[200px]">
              {projectName}
            </p>
          )}
        </div>
      </div>

      <div className="flex items-center gap-1">
        {onMinimize && (
          <Button
            variant="ghost"
            size="icon"
            onClick={onMinimize}
            className="h-9 w-9 hover:bg-muted rounded-xl"
            aria-label="Minimize chat"
          >
            <Minus className="h-4 w-4" />
          </Button>
        )}
        <Button
          variant="ghost"
          size="icon"
          onClick={onClose}
          className="h-9 w-9 hover:bg-muted rounded-xl"
          aria-label="Close chat"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};
